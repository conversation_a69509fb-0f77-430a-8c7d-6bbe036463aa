import { defineCollection, z } from "astro:content";
import { file } from "astro/loaders";

const company = defineCollection({
  loader: file("src/data/company.json"),
  schema: z.object({
    id: z.string(),
    name: z.string(),
    address: z.string(),
    phone: z.string(),
    email: z.string(),
    website: z.string(),
    industry: z.string(),
    description: z.string(),
    logo: z.string(),
    image: z.string(),
    tags: z.array(z.string()),
  }),
});

const person = defineCollection({
  loader: file("src/data/person.json"),
  schema: z.object({
    id: z.string(),
    name: z.string(),
  }),
});

export const collections = {
  company,
  person,
};
