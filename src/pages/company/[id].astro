---
import BaseLayout from "@/layouts/BaseLayout.astro";
import CompanyBasicInfo from "@/components/app/company-basic-info.astro";
import { getCollection } from "astro:content";

export async function getStaticPaths() {
  const companies = await getCollection("company");
  return companies.map((company) => ({
    params: { id: company.id },
    props: { company },
  }));
}

const { company } = Astro.props;
---

<BaseLayout title={company.data.name}>
  <CompanyBasicInfo company={company} />
</BaseLayout>
