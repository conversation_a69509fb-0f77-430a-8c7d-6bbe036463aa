---
import BaseLayout from "@/layouts/BaseLayout.astro";
import PersonBasicInfo from "@/components/app/person-basic-info.astro";
import { getCollection } from "astro:content";

export async function getStaticPaths() {
  const people = await getCollection("person");
  return people.map((person) => ({
    params: { id: person.id },
    props: { person },
  }));
}

const { person } = Astro.props;
---

<BaseLayout title={person.data.name}>
  <PersonBasicInfo person={person} />
</BaseLayout>
