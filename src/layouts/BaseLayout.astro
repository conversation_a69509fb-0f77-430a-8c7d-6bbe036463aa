---
import "@/styles/global.css";

interface Props {
  title?: string;
  lang?: string;
}

const { title = "查企业_查老板_查风险_企业信息查询系统", lang = "zh-Hans" } =
  Astro.props;

const fullTitle = `企查查 - ${title}`;
---

<html lang={lang}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{fullTitle}</title>
  </head>
  <body>
    <div id="app">
      <slot />
    </div>
  </body>
</html>
